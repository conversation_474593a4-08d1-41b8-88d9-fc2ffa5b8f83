This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  2 SEP 2025 11:18
entering extended mode
 \write18 enabled.
 %&-line parsing enabled.
**./LNNL_MJO_E3SM_Proposal_Harmonized.tex
(LNNL_MJO_E3SM_Proposal_Harmonized.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen150
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen151
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen152
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip17
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks20
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks21
\eqnshift@=\dimen153
\alignsep@=\dimen154
\tagshift@=\dimen155
\tagwidth@=\dimen156
\totwidth@=\dimen157
\lineht@=\dimen158
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.s
ty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen159
\Gin@req@width=\dimen160
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip54
\bibsep=\skip55
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count283
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.s
ty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.s
ty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box54
\beforetitleunit=\skip56
\aftertitleunit=\skip57
\ttl@plus=\dimen161
\ttl@minus=\dimen162
\ttl@toksa=\toks24
\titlewidth=\dimen163
\titlewidthlast=\dimen164
\titlewidthfirst=\dimen165
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
.sty
Package: microtype 2024/12/12 v3.2 Micro-typographical refinements (RS)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count284
)
\MT@toks=\toks25
\MT@tempbox=\box55
\MT@count=\count285
LaTeX Info: Redefining \noprotrusionifhmode on input line 1075.
LaTeX Info: Redefining \leftprotrusion on input line 1076.
\MT@prot@toks=\toks26
LaTeX Info: Redefining \rightprotrusion on input line 1095.
LaTeX Info: Redefining \textls on input line 1437.
\MT@outer@kern=\dimen166
LaTeX Info: Redefining \microtypecontext on input line 2041.
LaTeX Info: Redefining \textmicrotypecontext on input line 2058.
\MT@listname@count=\count286

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
-pdftex.def
File: microtype-pdftex.def 2024/12/12 v3.2 Definitions specific to pdftex (RS)
LaTeX Info: Redefining \lsstyle on input line 944.
LaTeX Info: Redefining \lslig on input line 944.
\MT@outer@space=\skip58
)
Package microtype Info: Loading configuration file microtype.cfg.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
.cfg
File: microtype.cfg 2024/12/12 v3.2 microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3053.
)
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 3
4.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count287
\l__pdf_internal_box=\box56
) (LNNL_MJO_E3SM_Proposal_Harmonized.aux)
\openout1 = `LNNL_MJO_E3SM_Proposal_Harmonized.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count288
\scratchdimen=\dimen167
\scratchbox=\box57
\nofMPsegments=\count289
\nofMParguments=\count290
\everyMPshowfont=\toks27
\MPscratchCnt=\count291
\MPscratchDim=\dimen168
\MPnumerator=\count292
\makeMPintoPDFobject=\count293
\everyMPtoPDFconversion=\toks28
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
LaTeX Info: Redefining \microtypecontext on input line 34.
Package microtype Info: Applying patch `item' on input line 34.
Package microtype Info: Applying patch `toc' on input line 34.
Package microtype Info: Applying patch `eqnum' on input line 34.
Package microtype Info: Applying patch `footnote' on input line 34.
Package microtype Info: Applying patch `verbatim' on input line 34.
LaTeX Info: Redefining \microtypesetup on input line 34.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `alltext-nott'.
LaTeX Info: Redefining \showhyphens on input line 34.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of interword spacing.
Package microtype Info: No adjustment of character kerning.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\mt-ptm.cf
g
File: mt-ptm.cfg 2006/04/20 v1.7 microtype config. file: Times (RS)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\mt-cmr.cf
g
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman 
(RS)
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 36.

 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\mt-msa.cf
g
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 36.

 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\mt-msb.cf
g
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)

[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{C:/Users/
ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/base/8r.enc}]

[2]

[3]

[4]

[5]
Underfull \hbox (badness 1708) in paragraph at lines 140--141
[]\OT1/ptm/b/n/10.95 (+20) Pioneering Uncertainty-Aware Model-Observation In-te
-gra-tion: \OT1/ptm/m/n/10.95 (+20) The sys-tem-atic in-te-gra-tion
 []


Underfull \hbox (badness 1259) in paragraph at lines 144--145
[]\OT1/ptm/b/n/10.95 (+20) Probabilistic Pro-cess Un-der-stand-ing: \OT1/ptm/m/
n/10.95 (+20) Com-pre-hen-sive char-ac-ter-i-za-tion of MJO-gravity wave
 []


Underfull \hbox (badness 10000) in paragraph at lines 146--147
[]\OT1/ptm/b/n/10.95 (+20) Uncertainty-Constrained Pa-ram-e-ter-i-za-tions: \OT
1/ptm/m/n/10.95 (+20) De-vel-op-ment of observationally-constrained
 []



[6]

[7]
Underfull \hbox (badness 2903) in paragraph at lines 192--193
[]\OT1/ptm/m/n/10.95 (+20) Madden, R. A., and P. R. Ju-lian (1971), De-tec-tion
 of a 40-50 day os-cil-la-tion in the
 []


Underfull \hbox (badness 5388) in paragraph at lines 192--193
\OT1/ptm/m/n/10.95 (+20) zonal wind in the trop-i-cal Pa-cific, \OT1/ptm/m/it/1
0.95 (+20) J. At-mos. Sci.\OT1/ptm/m/n/10.95 (+20) , 28, 702-708. DOI: 10.1175/
1520-
 []


Underfull \hbox (badness 10000) in paragraph at lines 194--195
[]\OT1/ptm/m/n/10.95 (+20) Zhang, C. (2005), Madden-Julian os-cil-la-tion, \OT1
/ptm/m/it/10.95 (+20) Rev. Geo-phys.\OT1/ptm/m/n/10.95 (+20) , 43, RG2003. DOI:

 []



[8]

[9]
Underfull \hbox (badness 2828) in paragraph at lines 232--233
\OT1/ptm/m/n/10.95 (+20) by the strato-spheric quasi-biennial os-cil-la-tion, \
OT1/ptm/m/it/10.95 (+20) Geo-phys. Res. Lett.\OT1/ptm/m/n/10.95 (+20) , 43, 139
2-1398. DOI:
 []



[10]

[11] (LNNL_MJO_E3SM_Proposal_Harmonized.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********
 ) 
Here is how much of TeX's memory you used:
 6159 strings out of 473904
 97469 string characters out of 5724713
 1935908 words of memory out of 5000000
 28731 multiletter control sequences out of 15000+600000
 586187 words of font info for 225 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,6n,65p,1032b,392s stack positions out of 10000i,1000n,20000p,200000b,200000s
<C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts
/cm/cmex10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/pu
blic/amsfonts/cm/cmmi10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/f
onts/type1/public/amsfonts/cm/cmmi6.pfb><C:/Users/<USER>/AppData/Local/Progr
ams/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi8.pfb><C:/Users/<USER>/AppData
/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr10.pfb><C:/Users/<USER>
unde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr8.pfb><C:/
Users/ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cm
sy10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/a
msfonts/cm/cmsy6.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/ty
pe1/urw/times/utmb8a.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/font
s/type1/urw/times/utmr8a.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/
fonts/type1/urw/times/utmri8a.pfb>
Output written on LNNL_MJO_E3SM_Proposal_Harmonized.pdf (11 pages, 162003 bytes
).
PDF statistics:
 94 PDF objects out of 1000 (max. 8388607)
 0 named destinations out of 1000 (max. 500000)
 32769 words of extra memory for PDF output out of 35830 (max. 10000000)

