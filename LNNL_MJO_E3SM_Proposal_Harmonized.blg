This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: LNNL_MJO_E3SM_Proposal_Harmonized.aux
Reallocating 'name_of_file' (item size: 1) to 6 items.
The style file: unsrt.bst
Reallocating 'name_of_file' (item size: 1) to 20 items.
Database file #1: LNNL_MJO_References.bib
You've used 47 entries,
            1791 wiz_defined-function locations,
            719 strings with 13871 characters,
and the built_in function-call counts, 13861 in all, are:
= -- 1170
> -- 802
< -- 0
+ -- 284
- -- 237
* -- 1290
:= -- 2266
add.period$ -- 141
call.type$ -- 47
change.case$ -- 47
chr.to.int$ -- 0
cite$ -- 47
duplicate$ -- 423
empty$ -- 1229
format.name$ -- 237
if$ -- 2898
int.to.chr$ -- 0
int.to.str$ -- 47
missing$ -- 47
newline$ -- 238
num.names$ -- 47
pop$ -- 47
preamble$ -- 1
purify$ -- 0
quote$ -- 0
skip$ -- 152
stack$ -- 0
substring$ -- 1422
swap$ -- 47
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 0
warning$ -- 0
while$ -- 127
width$ -- 49
write$ -- 519
